package com.wonderslate_v2

import grails.gorm.transactions.Transactional
import grails.plugin.springsecurity.SpringSecurityService
import com.wonderslate_v2.usermanagement.User

class AuthenticationController {

    SpringSecurityService springSecurityService
    AuthenticationService authenticationService

    def index() {
        redirect(action: 'login')
    }

    def login() {
        if (springSecurityService.isLoggedIn()) {
            redirect(controller: 'home', action: 'index')
            return
        }
        [:]
    }

    @Transactional
    def authenticate(){
        String username = "1_"+params.username
        def user = authenticationService.authenticate(username)

        println(user.username)
        println(user.password)
    }

}
