package com.wonderslate_v2

import grails.gorm.transactions.Transactional
import grails.plugin.springsecurity.SpringSecurityService
import com.wonderslate_v2.usermanagement.User

class AuthenticationController {

    SpringSecurityService springSecurityService
    AuthenticationService authenticationService

    def index() {
        redirect(action: 'login')
    }

    def login() {
        if (springSecurityService.isLoggedIn()) {
            redirect(controller: 'home', action: 'index')
            return
        }
        [:]
    }

    def authenticate(){
        String username = params.username
        def user = authenticationService.authenticate(username)

        println(user.username)
        println(user.password)

        render(status: 200, text: "Authentication successful for user: ${user.username}")
    }

}
