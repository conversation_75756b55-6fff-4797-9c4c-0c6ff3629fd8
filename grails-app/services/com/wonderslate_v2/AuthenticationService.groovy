package com.wonderslate_v2

import grails.gorm.transactions.Transactional
import com.wonderslate_v2.usermanagement.User
import grails.plugin.springsecurity.SpringSecurityService

class AuthenticationService {
    SpringSecurityService springSecurityService

    def serviceMethod() {

    }

    @Transactional('wsuser')
    def authenticate(String rawUsername, String rawPassword) {
        String username = "1_" + rawUsername
        def user = User.findByUsername(username)

        if (!user) {
            throw new RuntimeException("User not found")
        }

        // Check if password encoder is available and password matches
        if (springSecurityService?.passwordEncoder) {
            if (!springSecurityService.passwordEncoder.matches(rawPassword, user.password)) {
                throw new RuntimeException("Invalid password")
            }
        } else {
            // Fallback for simple password comparison if no encoder is configured
            if (user.password != rawPassword) {
                throw new RuntimeException("Invalid password")
            }
        }

        return user
    }
}
