package com.wonderslate_v2

import grails.gorm.transactions.Transactional
import com.wonderslate_v2.usermanagement.User

class AuthenticationService {

    def serviceMethod() {

    }

    @Transactional('wsuser')
    def authenticate(String rawUsername) {
        String username = "1_" + rawUsername
        def user = User.findByUsername(username)

        if (!user) {
            throw new RuntimeException("User not found")
        }

        return user
    }
}
